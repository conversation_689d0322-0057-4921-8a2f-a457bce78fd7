import React from 'react';
import { Star, TrendingUp, Alert<PERSON>riangle, Check, X, Trophy, Target, Loader2 } from 'lucide-react';
import { useUSPAnalysis } from '../hooks/useResearchAnalysis';

const uspAnalysis = [
  {
    usp: 'AI-Powered Full-Cycle Development',
    strength: 'High',
    uniqueness: 'Very High',
    market_demand: 'High',
    sustainability: 'High',
    status: 'Winning'
  },
  {
    usp: '70% Development Time Reduction',
    strength: 'High',
    uniqueness: 'Medium',
    market_demand: 'Very High',
    sustainability: 'Medium',
    status: 'Winning'
  },
  {
    usp: 'Enterprise-Grade Security',
    strength: 'Medium',
    uniqueness: 'Low',
    market_demand: 'High',
    sustainability: 'High',
    status: 'Risky'
  },
  {
    usp: 'Multi-Language Support',
    strength: 'Medium',
    uniqueness: 'Low',
    market_demand: 'Medium',
    sustainability: 'Low',
    status: 'Losing'
  }
];

const competitors = [
  {
    name: 'CodeFlow AI',
    ai_integration: 9,
    development_speed: 9,
    code_quality: 8,
    enterprise_features: 8,
    pricing: 7,
    market_position: 'Challenger'
  },
  {
    name: 'GitHub Copilot',
    ai_integration: 8,
    development_speed: 7,
    code_quality: 7,
    enterprise_features: 9,
    pricing: 8,
    market_position: 'Leader'
  },
  {
    name: 'Tabnine',
    ai_integration: 7,
    development_speed: 6,
    code_quality: 6,
    enterprise_features: 6,
    pricing: 8,
    market_position: 'Follower'
  },
  {
    name: 'Replit',
    ai_integration: 6,
    development_speed: 8,
    code_quality: 5,
    enterprise_features: 4,
    pricing: 9,
    market_position: 'Niche'
  }
];

interface UniqueSellingPointsProps {
  selectedResearch?: {
    id: string;
    title: string;
    description: string;
    project_type: string;
    industry?: string;
  };
}

export function UniqueSellingPoints({ selectedResearch }: UniqueSellingPointsProps) {
  const { data: analysisData, loading, error } = useUSPAnalysis(selectedResearch?.id);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading USP analysis...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <AlertTriangle className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Analysis</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Show message when no research is selected
  if (!selectedResearch) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Research Selected</h3>
          <p className="text-gray-600">Please select a research project to view the USP analysis.</p>
        </div>
      </div>
    );
  }

  // Extract USP data from analysis
  const uspAnalysisData = analysisData?.usp_propositions?.usps || analysisData?.usp_analysis?.usps || uspAnalysis;
  const competitorsData = analysisData?.usp_competitive_analysis?.competitors || competitors;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Winning': return 'text-green-600 bg-green-100';
      case 'Risky': return 'text-yellow-600 bg-yellow-100';
      case 'Losing': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };



  const getStrengthColor = (strength: string) => {
    switch (strength) {
      case 'Very High': return 'bg-green-500';
      case 'High': return 'bg-green-400';
      case 'Medium': return 'bg-yellow-400';
      case 'Low': return 'bg-red-400';
      default: return 'bg-gray-400';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Unique Selling Points</h1>
        <p className="text-gray-600">
          Comprehensive analysis of {selectedResearch.title}'s unique value propositions, competitive positioning,
          and recommendations for strengthening market differentiation.
        </p>
      </div>

      {/* Unique Selling Propositions */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Unique Selling Propositions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            {analysisData?.usp_primary && (
              <div className="p-6 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg">
                <h3 className="text-lg font-semibold mb-2">Primary USP</h3>
                <p className="text-blue-100 text-sm mb-4">
                  {analysisData.usp_primary.description || analysisData.usp_primary}
                </p>
                <div className="flex items-center space-x-2">
                  <Star className="w-4 h-4" />
                  <span className="text-sm font-medium">Key Market Differentiation</span>
                </div>
              </div>
            )}

            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Supporting USPs</h4>
              {analysisData?.usp_supporting?.length > 0 ? (
                <ul className="space-y-2 text-sm text-gray-700">
                  {analysisData.usp_supporting.map((usp: string, index: number) => (
                    <li key={index} className="flex items-center space-x-2">
                      <Check className="w-4 h-4 text-green-500" />
                      <span>{usp}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm">Supporting USPs analysis is not yet available.</p>
              )}
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-semibold text-green-900 mb-2">Value Propositions</h4>
              <div className="space-y-3">
                {analysisData?.usp_value_propositions ? (
                  Object.entries(analysisData.usp_value_propositions).map(([segment, value]) => (
                    <div key={segment}>
                      <h5 className="font-medium text-green-800 capitalize">
                        {segment.replace(/([A-Z])/g, ' $1').trim()}
                      </h5>
                      <p className="text-sm text-green-700">{value}</p>
                    </div>
                  ))
                ) : (
                  <p className="text-green-700 text-sm">Value propositions analysis is not yet available.</p>
                )}
              </div>
            </div>

            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h4 className="font-semibold text-orange-900 mb-2">Competitive Moats</h4>
              {analysisData?.usp_competitive_moats?.length > 0 ? (
                <ul className="space-y-1 text-sm text-orange-700">
                  {analysisData.usp_competitive_moats.map((moat: string, index: number) => (
                    <li key={index}>• {moat}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-orange-700 text-sm">Competitive moats analysis is not yet available.</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* USP Acid Test */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">The Early USP Acid Test</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">USP</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Strength</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Uniqueness</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Market Demand</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Sustainability</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
              </tr>
            </thead>
            <tbody>
              {uspAnalysisData.length > 0 ? (
                uspAnalysisData.map((usp: any, index: number) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-3 px-4 font-medium text-gray-900">{usp.usp || usp.name || usp.title}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.strength || 'Medium')}`}>
                        {usp.strength || 'N/A'}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.uniqueness || 'Medium')}`}>
                        {usp.uniqueness || 'N/A'}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.market_demand || 'Medium')}`}>
                        {usp.market_demand || 'N/A'}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.sustainability || 'Medium')}`}>
                        {usp.sustainability || 'N/A'}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        {usp.status === 'Winning' && <Trophy className="w-4 h-4 text-green-500" />}
                        {usp.status === 'Risky' && <AlertTriangle className="w-4 h-4 text-yellow-500" />}
                        {usp.status === 'Losing' && <X className="w-4 h-4 text-red-500" />}
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(usp.status || 'Risky')}`}>
                          {usp.status || 'Pending'}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="py-8 text-center text-gray-500">
                    USP acid test analysis is not yet available for this research.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* What Makes You Unique */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">What Makes You Unique?</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {analysisData?.usp_unique_factors?.length > 0 ? (
            analysisData.usp_unique_factors.slice(0, 3).map((factor: any, index: number) => {
              const colors = ['blue', 'green', 'purple'];
              const icons = [Target, TrendingUp, Star];
              const color = colors[index % colors.length];
              const Icon = icons[index % icons.length];

              return (
                <div key={index} className={`p-4 bg-${color}-50 border border-${color}-200 rounded-lg`}>
                  <div className={`w-12 h-12 bg-${color}-600 rounded-lg flex items-center justify-center mb-3`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className={`font-semibold text-${color}-900 mb-2`}>
                    {factor.title || factor.name || `Unique Factor ${index + 1}`}
                  </h3>
                  <p className={`text-sm text-${color}-700`}>
                    {factor.description || factor}
                  </p>
                </div>
              );
            })
          ) : (
            <div className="col-span-3 text-center py-8">
              <p className="text-gray-500">Unique factors analysis is not yet available for this research.</p>
            </div>
          )}
        </div>
      </div>

      {/* USP Examples */}
      {analysisData?.usp_examples && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Good USP Examples</h2>
            <div className="space-y-4">
              {analysisData.usp_examples.good?.map((example: any, index: number) => (
                <div key={index} className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <Check className="w-4 h-4 text-green-600" />
                    <h3 className="font-semibold text-green-900">{example.company || example.name}</h3>
                  </div>
                  <p className="text-sm text-green-700 mb-2">
                    "{example.usp || example.proposition}"
                  </p>
                  <p className="text-xs text-green-600">
                    {example.reason || example.explanation}
                  </p>
                </div>
              )) || (
                <p className="text-gray-500">Good USP examples are not yet available.</p>
              )}
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Bad USP Examples</h2>
            <div className="space-y-4">
              {analysisData.usp_examples.bad?.map((example: any, index: number) => (
                <div key={index} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-2">
                    <X className="w-4 h-4 text-red-600" />
                    <h3 className="font-semibold text-red-900">{example.company || example.name || 'Example'}</h3>
                  </div>
                  <p className="text-sm text-red-700 mb-2">
                    "{example.usp || example.proposition}"
                  </p>
                  <p className="text-xs text-red-600">
                    {example.reason || example.explanation}
                  </p>
                </div>
              )) || (
                <p className="text-gray-500">Bad USP examples are not yet available.</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Ranking Competitors USP Analysis */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Competitive USP Analysis</h2>
        <p className="text-gray-600 mb-6">
          Comparison matrix ranking key criteria from 1-10 (higher is better)
        </p>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">Company</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">AI Integration</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Development Speed</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Code Quality</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Enterprise Features</th>
                <th className="text-center py-3 px-4 font-medium text-gray-700">Pricing</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Position</th>
              </tr>
            </thead>
            <tbody>
              {competitorsData.length > 0 ? (
                competitorsData.map((competitor: any, index: number) => (
                <tr key={index} className="border-b border-gray-100">
                  <td className="py-3 px-4 font-medium text-gray-900">{competitor.name}</td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.ai_integration * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.ai_integration}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.development_speed * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.development_speed}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.code_quality * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.code_quality}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.enterprise_features * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.enterprise_features}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-center">
                    <div className="flex items-center justify-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${getStrengthColor('High')}`}
                          style={{ width: `${competitor.pricing * 10}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{competitor.pricing}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      competitor.market_position === 'Leader' ? 'bg-green-100 text-green-800' :
                      competitor.market_position === 'Challenger' ? 'bg-blue-100 text-blue-800' :
                      competitor.market_position === 'Follower' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {competitor.market_position}
                    </span>
                  </td>
                </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="py-8 text-center text-gray-500">
                    Competitive USP analysis is not yet available for this research.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}