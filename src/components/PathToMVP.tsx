import React, { useState } from 'react';
import { CheckCircle, Clock, DollarSign, Target, TrendingUp, Users, Loader2, AlertTriangle } from 'lucide-react';
import { useMVPAnalysis } from '../hooks/useResearchAnalysis';

interface PathToMVPProps {
  selectedResearch?: {
    id: string;
    title: string;
    description: string;
    project_type: string;
    industry?: string;
  };
}

export function PathToMVP({ selectedResearch }: PathToMVPProps) {
  const { data: analysisData, loading, error } = useMVPAnalysis(selectedResearch?.id);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading MVP analysis...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <AlertTriangle className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Analysis</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Show message when no research is selected
  if (!selectedResearch) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Research Selected</h3>
          <p className="text-gray-600">Please select a research project to view the MVP analysis.</p>
        </div>
      </div>
    );
  }

  // Extract data from analysis
  const coreFeatures = analysisData?.mvp_core_features?.features || [];
  const timeline = analysisData?.mvp_development_timeline?.phases || [];
  const marketingStrategy = analysisData?.mvp_marketing_strategy?.channels || [];
  const performanceMetrics = analysisData?.mvp_performance_metrics?.metrics || [];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Path to MVP</h1>
        <p className="text-gray-600">
          Strategic roadmap for {selectedResearch.title} from concept to market launch, including core feature development,
          market validation, marketing strategy, and key performance indicators.
        </p>
      </div>

      {/* Core Features */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Core Features Development</h2>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">Feature</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Priority</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Development Time</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
              </tr>
            </thead>
            <tbody>
              {coreFeatures.length > 0 ? (
                coreFeatures.map((item, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-3">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="font-medium text-gray-900">{item.feature || item.name || item.title}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.priority === 'High' ? 'bg-red-100 text-red-800' :
                        item.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.priority || 'N/A'}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-gray-600">{item.effort || item.duration || 'N/A'}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        item.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                        item.status === 'Planning' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {item.status || 'Planned'}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="py-8 text-center text-gray-500">
                    Core features analysis is not yet available for this research.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Market Validation & Timeline */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Market Validation</h2>
          <div className="space-y-4">
            {analysisData?.mvp_market_validation ? (
              Object.entries(analysisData.mvp_market_validation).map(([key, value], index) => {
                const colors = ['green', 'blue', 'purple'];
                const color = colors[index % colors.length];
                return (
                  <div key={key} className={`p-4 bg-${color}-50 border border-${color}-200 rounded-lg`}>
                    <h3 className={`font-semibold text-${color}-900 mb-2 capitalize`}>
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </h3>
                    {typeof value === 'string' ? (
                      <p className={`text-sm text-${color}-700`}>{value}</p>
                    ) : Array.isArray(value) ? (
                      <ul className={`text-xs text-${color}-600 space-y-1`}>
                        {value.map((item, idx) => (
                          <li key={idx}>• {item}</li>
                        ))}
                      </ul>
                    ) : (
                      <div className={`text-sm text-${color}-700`}>
                        {JSON.stringify(value, null, 2)}
                      </div>
                    )}
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">Market validation analysis is not yet available for this research.</p>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Timeline & Milestones</h2>
          <div className="space-y-4">
            {timeline.length > 0 ? (
              timeline.map((phase, index) => (
                <div key={index} className="flex items-center space-x-4">
                  <div className={`w-4 h-4 rounded-full ${
                    phase.status === 'current' ? 'bg-blue-500' :
                    phase.status === 'completed' ? 'bg-green-500' :
                    'bg-gray-300'
                  }`}></div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{phase.phase || phase.name || phase.title}</h3>
                    <p className="text-sm text-gray-500">{phase.description || phase.duration}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{phase.duration || 'TBD'}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">Timeline analysis is not yet available for this research.</p>
              </div>
            )}
          </div>
          
          {analysisData?.mvp_milestones && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-2">Key Milestones</h3>
              <div className="space-y-2 text-sm">
                {Object.entries(analysisData.mvp_milestones).map(([milestone, timing]) => (
                  <div key={milestone} className="flex justify-between">
                    <span className="text-gray-600 capitalize">{milestone.replace(/([A-Z])/g, ' $1').trim()}</span>
                    <span className="font-medium text-gray-900">{timing}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Marketing Strategy */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Marketing Strategy</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {marketingStrategy.length > 0 ? (
            marketingStrategy.map((channel, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">{channel.channel || channel.name}</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Reach:</span>
                    <span className="font-medium">{channel.reach || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Investment:</span>
                    <span className="font-medium text-orange-600">{channel.cost || channel.investment || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expected ROI:</span>
                    <span className="font-medium text-green-600">{channel.roi || 'N/A'}</span>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-4 text-center py-8">
              <p className="text-gray-500">Marketing strategy analysis is not yet available for this research.</p>
            </div>
          )}
        </div>
      </div>

      {/* Budget Considerations */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Budget Considerations</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
              <Target className="w-4 h-4 mr-2" />
              Development Costs
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-700">Engineering Team</span>
                <span className="font-medium">$180K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">Infrastructure</span>
                <span className="font-medium">$25K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-700">AI/ML Services</span>
                <span className="font-medium">$35K</span>
              </div>
              <div className="border-t border-blue-200 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-blue-900">Total</span>
                  <span className="text-blue-900">$240K</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-3 flex items-center">
              <TrendingUp className="w-4 h-4 mr-2" />
              Marketing Costs
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-green-700">Digital Marketing</span>
                <span className="font-medium">$50K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Content Creation</span>
                <span className="font-medium">$20K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-700">Events & Conferences</span>
                <span className="font-medium">$30K</span>
              </div>
              <div className="border-t border-green-200 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-green-900">Total</span>
                  <span className="text-green-900">$100K</span>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <h3 className="font-semibold text-orange-900 mb-3 flex items-center">
              <DollarSign className="w-4 h-4 mr-2" />
              Operating Costs
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-orange-700">Legal & Compliance</span>
                <span className="font-medium">$15K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-orange-700">Office & Equipment</span>
                <span className="font-medium">$10K</span>
              </div>
              <div className="flex justify-between">
                <span className="text-orange-700">Contingency (10%)</span>
                <span className="font-medium">$36K</span>
              </div>
              <div className="border-t border-orange-200 pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span className="text-orange-900">Total</span>
                  <span className="text-orange-900">$61K</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-gray-900 text-white rounded-lg">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Total MVP Investment Required</h3>
            <span className="text-2xl font-bold">$401K</span>
          </div>
          <p className="text-gray-300 text-sm mt-2">
            Investment covers 6-month runway to reach public launch and initial traction
          </p>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Performance Metrics</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {performanceMetrics.length > 0 ? (
            performanceMetrics.map((metric, index) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg text-center">
                <h3 className="font-semibold text-gray-900 mb-2">{metric.metric || metric.name}</h3>
                <div className="text-2xl font-bold text-blue-600 mb-1">{metric.target || metric.value}</div>
                <div className="text-sm text-gray-500">{metric.timeframe || metric.period}</div>
              </div>
            ))
          ) : (
            <div className="col-span-4 text-center py-8">
              <p className="text-gray-500">Performance metrics analysis is not yet available for this research.</p>
            </div>
          )}
        </div>
        
        {analysisData?.mvp_success_criteria && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-3">Success Criteria</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {Object.entries(analysisData.mvp_success_criteria).map(([category, criteria]) => (
                <div key={category}>
                  <h4 className="font-medium text-blue-800 mb-2 capitalize">
                    {category.replace(/([A-Z])/g, ' $1').trim()}
                  </h4>
                  <ul className="space-y-1 text-blue-700">
                    {Array.isArray(criteria) ? (
                      criteria.map((item, idx) => (
                        <li key={idx}>• {item}</li>
                      ))
                    ) : (
                      <li>• {criteria}</li>
                    )}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}