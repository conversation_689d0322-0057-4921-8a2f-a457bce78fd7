import React from 'react';
import { DollarSign, <PERSON><PERSON><PERSON><PERSON>p, <PERSON><PERSON>hart, Target, AlertTriangle, BarChart3, Loader2, Cal<PERSON>tor } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar<PERSON>hart, Bar, <PERSON><PERSON>hart as RechartsPie, Cell, AreaChart, Area } from 'recharts';
import { useFinancesAnalysis } from '../hooks/useResearchAnalysis';

const revenueProjection = [
  { month: 'M1', revenue: 0, users: 0 },
  { month: 'M3', revenue: 15000, users: 500 },
  { month: 'M6', revenue: 45000, users: 1500 },
  { month: 'M9', revenue: 100000, users: 3000 },
  { month: 'M12', revenue: 180000, users: 5000 },
  { month: 'M18', revenue: 320000, users: 8000 },
  { month: 'M24', revenue: 500000, users: 12000 },
  { month: 'M36', revenue: 850000, users: 18000 },
  { month: 'M48', revenue: 1200000, users: 25000 },
  { month: 'M60', revenue: 1500000, users: 30000 },
];

const operatingExpenses = [
  { category: 'Salaries', amount: 180000, percentage: 45 },
  { category: 'Infrastructure', amount: 60000, percentage: 15 },
  { category: 'Marketing', amount: 80000, percentage: 20 },
  { category: 'Operations', amount: 40000, percentage: 10 },
  { category: 'Miscellaneous', amount: 40000, percentage: 10 },
];

const breakeven = [
  { month: 'M1', revenue: 0, expenses: 33333 },
  { month: 'M3', revenue: 15000, expenses: 33333 },
  { month: 'M6', revenue: 45000, expenses: 33333 },
  { month: 'M9', revenue: 100000, expenses: 33333 },
  { month: 'M12', revenue: 180000, expenses: 33333 },
  { month: 'M15', revenue: 250000, expenses: 33333 },
  { month: 'M18', revenue: 320000, expenses: 33333 },
];

const kpis = [
  { metric: 'Customer Acquisition Cost (CAC)', value: '$125', target: '<$150', status: 'good' },
  { metric: 'Lifetime Value (LTV)', value: '$2,400', target: '>$2,000', status: 'good' },
  { metric: 'LTV/CAC Ratio', value: '19.2x', target: '>3x', status: 'excellent' },
  { metric: 'Monthly Churn Rate', value: '3.2%', target: '<5%', status: 'good' },
  { metric: 'Monthly Recurring Revenue', value: '$180K', target: '$100K', status: 'excellent' },
  { metric: 'Average Revenue Per User', value: '$36', target: '$30', status: 'good' },
];

const COLORS = ['#3B82F6', '#10B981', '#F97316', '#8B5CF6', '#EF4444'];

interface FinancesProps {
  selectedResearch?: {
    id: string;
    title: string;
    description: string;
    project_type: string;
    industry?: string;
  };
}

export function Finances({ selectedResearch }: FinancesProps) {
  const { data: analysisData, loading, error } = useFinancesAnalysis(selectedResearch?.id);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading financial analysis...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <AlertTriangle className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Analysis</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Show message when no research is selected
  if (!selectedResearch) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Research Selected</h3>
          <p className="text-gray-600">Please select a research project to view the financial analysis.</p>
        </div>
      </div>
    );
  }

  // Extract financial data from analysis or use defaults
  const revenueData = analysisData?.finances_revenue_projection?.data || revenueProjection;
  const expensesData = analysisData?.finances_operating_expenses?.data || operatingExpenses;
  const metricsData = analysisData?.finances_key_metrics?.data || keyMetrics;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100';
      case 'good': return 'text-blue-600 bg-blue-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'poor': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Financial Analysis</h1>
        <p className="text-gray-600">
          Comprehensive financial modeling for {selectedResearch.title} including market research, startup costs,
          revenue projections, operational expenses, and key performance indicators.
        </p>
      </div>

      {/* Market Research */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Market Research</h2>
        {analysisData?.finances_market_research ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">Total Market Size</h3>
              <div className="text-3xl font-bold text-blue-600 mb-2">
                {analysisData.finances_market_research.totalMarketSize || '$450B'}
              </div>
              <p className="text-sm text-blue-700 mb-4">
                {analysisData.finances_market_research.description || 'Global software development tools market'}
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-600">Growth Rate:</span>
                  <span className="font-medium">{analysisData.finances_market_research.growthRate || '12.5% CAGR'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-600">Time Frame:</span>
                  <span className="font-medium">{analysisData.finances_market_research.timeFrame || '2024-2029'}</span>
                </div>
              </div>
            </div>

            <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="text-lg font-semibold text-green-900 mb-4">Target Market</h3>
              <div className="text-3xl font-bold text-green-600 mb-2">
                {analysisData.finances_market_research.targetMarketSize || '$120B'}
              </div>
              <p className="text-sm text-green-700 mb-4">
                {analysisData.finances_market_research.targetDescription || 'AI-powered development tools segment'}
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-green-600">Target Users:</span>
                  <span className="font-medium">{analysisData.finances_market_research.targetUsers || '2.8M developers'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-green-600">Enterprise Seg:</span>
                  <span className="font-medium">{analysisData.finances_market_research.enterpriseSegment || '45% of market'}</span>
                </div>
              </div>
            </div>

            <div className="p-6 bg-orange-50 border border-orange-200 rounded-lg">
              <h3 className="text-lg font-semibold text-orange-900 mb-4">Competitive Analysis</h3>
              <div className="space-y-3">
                {analysisData.finances_market_research.competitors?.map((competitor: any, index: number) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="text-sm font-medium text-orange-700">{competitor.name}</span>
                    <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded">{competitor.position}</span>
                  </div>
                )) || (
                  <p className="text-orange-700 text-sm">Competitive analysis data not available.</p>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Market research analysis is not yet available for this research.</p>
          </div>
        )}
      </div>

      {/* Startup Costs */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Startup Costs</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Investment Breakdown</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Platform Development</h4>
                  <p className="text-sm text-gray-600">Engineering, AI/ML infrastructure</p>
                </div>
                <span className="text-lg font-bold text-gray-900">$240K</span>
              </div>
              
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Marketing Campaign</h4>
                  <p className="text-sm text-gray-600">Digital marketing, content, events</p>
                </div>
                <span className="text-lg font-bold text-gray-900">$100K</span>
              </div>
              
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Content Creation</h4>
                  <p className="text-sm text-gray-600">Documentation, tutorials, demos</p>
                </div>
                <span className="text-lg font-bold text-gray-900">$25K</span>
              </div>
              
              <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Initial Staffing</h4>
                  <p className="text-sm text-gray-600">First 6 months operational costs</p>
                </div>
                <span className="text-lg font-bold text-gray-900">$60K</span>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-900 text-white rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">Total Investment Required</span>
                <span className="text-2xl font-bold">$425K</span>
              </div>
              <p className="text-blue-200 text-sm mt-2">6-month runway to reach profitability</p>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Cost Distribution</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPie>
                  <RechartsPie
                    data={[
                      { name: 'Development', value: 240000 },
                      { name: 'Marketing', value: 100000 },
                      { name: 'Operations', value: 60000 },
                      { name: 'Content', value: 25000 },
                    ]}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: $${(value/1000).toFixed(0)}K`}
                  >
                    {[
                      { name: 'Development', value: 240000 },
                      { name: 'Marketing', value: 100000 },
                      { name: 'Operations', value: 60000 },
                      { name: 'Content', value: 25000 },
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </RechartsPie>
                  <Tooltip formatter={(value) => `$${(value/1000).toFixed(0)}K`} />
                </RechartsPie>
              </ResponsiveContainer>
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <p className="mb-2"><strong>Key Assumptions:</strong></p>
              <ul className="space-y-1">
                <li>• 8-person engineering team for 6 months</li>
                <li>• AI/ML infrastructure costs included</li>
                <li>• Marketing spend across multiple channels</li>
                <li>• Conservative timeline with buffer for delays</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Projection */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Revenue Projection</h2>
        <div className="h-80 mb-6">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="revenue" stroke="#3B82F6" strokeWidth={3} />
                <Line type="monotone" dataKey="users" stroke="#10B981" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analysisData?.finances_revenue_summary?.yearFiveARR || '$1.5M'}
              </div>
              <div className="text-sm text-blue-700">Year 5 ARR</div>
            </div>
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-600">
                {analysisData?.finances_revenue_summary?.activeUsers || '30K'}
              </div>
              <div className="text-sm text-green-700">Active Users</div>
            </div>
            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg text-center">
              <div className="text-2xl font-bold text-orange-600">
                {analysisData?.finances_revenue_summary?.arpu || '$50'}
              </div>
              <div className="text-sm text-orange-700">ARPU</div>
            </div>
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-600">
                {analysisData?.finances_revenue_summary?.grossMargin || '85%'}
              </div>
              <div className="text-sm text-purple-700">Gross Margin</div>
            </div>
          </div>

      </div>

      {/* Operating Expenses */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Operating Expenses</h2>
          <div className="space-y-4">
            {expensesData.map((expense: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }}></div>
                  <span className="font-medium text-gray-900">{expense.category}</span>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-gray-900">${(expense.amount/1000).toFixed(0)}K</div>
                  <div className="text-xs text-gray-500">{expense.percentage}% of total</div>
                </div>
              </div>
            ))}
            
            <div className="p-4 bg-gray-900 text-white rounded-lg mt-6">
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold">Annual Operating Expenses</span>
                <span className="text-2xl font-bold">$400K</span>
              </div>
              <p className="text-gray-300 text-sm mt-2">
                Based on 8-person team and cloud infrastructure
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Breakeven Analysis</h2>
          <div className="h-64 mb-6">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={breakeven}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => `$${(value/1000).toFixed(0)}K`} />
                <Bar dataKey="revenue" fill="#3B82F6" name="Revenue" />
                <Bar dataKey="expenses" fill="#EF4444" name="Expenses" />
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">Breakeven Point</h3>
            <div className="flex justify-between items-center mb-2">
              <span className="text-blue-700">Monthly Breakeven:</span>
              <span className="font-bold text-blue-900">Month 15</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-blue-700">Monthly Revenue Required:</span>
              <span className="font-bold text-blue-900">$33.3K</span>
            </div>
            <p className="text-xs text-blue-600 mt-2">
              Based on current burn rate and projected revenue growth
            </p>
          </div>
        </div>
      </div>

      {/* Funding & Risks */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Funding & Risks</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Funding Options</h3>
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Seed Round</h4>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded">Recommended</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  $500K - $1M from angel investors and early-stage VCs
                </p>
                <div className="text-xs text-gray-500">
                  <div className="flex justify-between mb-1">
                    <span>Timeline:</span>
                    <span>3-4 months</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Equity:</span>
                    <span>10-15%</span>
                  </div>
                </div>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Bootstrapping</h4>
                  <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">Alternative</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Self-funding with revenue from early customers
                </p>
                <div className="text-xs text-gray-500">
                  <div className="flex justify-between mb-1">
                    <span>Timeline:</span>
                    <span>12-18 months</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Equity:</span>
                    <span>0%</span>
                  </div>
                </div>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">Accelerator Program</h4>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded">Alternative</span>
                </div>
                <p className="text-sm text-gray-600 mb-2">
                  Y Combinator, Techstars or similar program
                </p>
                <div className="text-xs text-gray-500">
                  <div className="flex justify-between mb-1">
                    <span>Timeline:</span>
                    <span>3-6 months</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Equity:</span>
                    <span>7-10%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Risks</h3>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <h4 className="font-medium text-red-900">Market Timing Risk</h4>
                </div>
                <p className="text-sm text-red-700 mb-2">
                  Rapid evolution of AI tools market could lead to saturation
                </p>
                <div className="flex justify-between text-xs">
                  <span className="text-red-600">Mitigation:</span>
                  <span className="text-red-800">Accelerated go-to-market strategy</span>
                </div>
              </div>
              
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <h4 className="font-medium text-red-900">Technology Risk</h4>
                </div>
                <p className="text-sm text-red-700 mb-2">
                  AI model performance may not meet quality expectations
                </p>
                <div className="flex justify-between text-xs">
                  <span className="text-red-600">Mitigation:</span>
                  <span className="text-red-800">Phased rollout with extensive testing</span>
                </div>
              </div>
              
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <h4 className="font-medium text-red-900">Competitive Risk</h4>
                </div>
                <p className="text-sm text-red-700 mb-2">
                  Large tech companies may enter market with similar offerings
                </p>
                <div className="flex justify-between text-xs">
                  <span className="text-red-600">Mitigation:</span>
                  <span className="text-red-800">Focus on niche specialization</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Key Performance Indicators</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {kpis.map((kpi, index) => (
            <div key={index} className="p-4 border border-gray-200 rounded-lg">
              <h3 className="font-semibold text-gray-900 mb-3">{kpi.metric}</h3>
              <div className="flex justify-between items-center mb-2">
                <span className="text-gray-600">Current:</span>
                <span className="text-xl font-bold text-blue-600">{kpi.value}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Target:</span>
                <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(kpi.status)}`}>
                  {kpi.target}
                </span>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-3">KPI Insights</h3>
          <p className="text-sm text-blue-700 mb-4">
            CodeFlow AI is demonstrating strong early financial metrics, particularly in the LTV/CAC ratio which 
            indicates excellent unit economics. The current focus should be on maintaining the low CAC while 
            scaling user acquisition to accelerate growth.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Strengths</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• Exceptional LTV/CAC ratio (19.2x vs industry avg 3-5x)</li>
                <li>• Strong ARPU at $36 (20% above target)</li>
                <li>• Low churn rate indicating product-market fit</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-blue-800 mb-2">Focus Areas</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• Scale marketing while maintaining CAC efficiency</li>
                <li>• Increase enterprise segment to improve ARPU</li>
                <li>• Develop upsell opportunities to increase LTV</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}