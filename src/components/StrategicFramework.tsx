import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Shield, AlertTriangle, TrendingUp, Zap, Loader2 } from 'lucide-react';
import { useStrategicFrameworkAnalysis } from '../hooks/useResearchAnalysis';

const sections = [
  { id: 'intro', name: 'Introduction', icon: Shield },
  { id: 'strategy', name: 'Strategy', icon: TrendingUp },
  { id: 'swot', name: 'SWOT Analysis', icon: Shield },
  { id: 'pestel', name: 'PESTEL Analysis', icon: AlertTriangle },
  { id: 'porter', name: '<PERSON>\'s Five Forces', icon: Zap },
  { id: 'catwoe', name: 'CATWOE Analysis', icon: TrendingUp },
  { id: 'gamechanging', name: 'Game Changing Idea', icon: Zap },
];

interface StrategicFrameworkProps {
  selectedResearch?: {
    id: string;
    title: string;
    description: string;
    project_type: string;
    industry?: string;
  };
}

export function StrategicFramework({ selectedResearch }: StrategicFrameworkProps) {
  const [expandedSection, setExpandedSection] = useState('intro');
  const { data: analysisData, loading, error } = useStrategicFrameworkAnalysis(selectedResearch?.id);

  const toggleSection = (sectionId: string) => {
    setExpandedSection(expandedSection === sectionId ? '' : sectionId);
  };

  // Helper function to render section content
  const renderSectionContent = (sectionId: string) => {
    const sectionKey = `strategic_${sectionId}`;
    const sectionData = analysisData?.[sectionKey];

    if (!sectionData) {
      return (
        <div className="px-6 pb-6 border-t">
          <div className="pt-6 text-center py-8">
            <p className="text-gray-500">Analysis for this section is not yet available.</p>
          </div>
        </div>
      );
    }

    // Render different content based on section type
    switch (sectionId) {
      case 'intro':
        return (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 space-y-4">
              <p className="text-gray-700">{sectionData.introduction || sectionData.overview}</p>
              {sectionData.keyPoints && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  {sectionData.keyPoints.slice(0, 2).map((point: any, index: number) => (
                    <div key={index} className="p-4 bg-blue-50 rounded-lg">
                      <h4 className="font-semibold text-blue-900 mb-2">{point.title || `Key Point ${index + 1}`}</h4>
                      <p className="text-blue-700 text-sm">{point.description || point}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        );

      case 'strategy':
        return (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 space-y-6">
              {sectionData.overview && (
                <p className="text-gray-700">{sectionData.overview}</p>
              )}
              {sectionData.strategies && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {sectionData.strategies.map((strategy: any, index: number) => (
                    <div key={index} className="p-4 border border-gray-200 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">{strategy.title || strategy.name}</h4>
                      <p className="text-gray-600 text-sm">{strategy.description}</p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        );

      default:
        return (
          <div className="px-6 pb-6 border-t">
            <div className="pt-6 space-y-4">
              {typeof sectionData === 'string' ? (
                <p className="text-gray-700">{sectionData}</p>
              ) : (
                <div className="space-y-4">
                  {Object.entries(sectionData).map(([key, value]) => (
                    <div key={key} className="p-4 border border-gray-200 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</h4>
                      <div className="text-gray-600 text-sm">
                        {Array.isArray(value) ? (
                          <ul className="list-disc list-inside space-y-1">
                            {value.map((item: any, idx: number) => (
                              <li key={idx}>{typeof item === 'string' ? item : JSON.stringify(item)}</li>
                            ))}
                          </ul>
                        ) : typeof value === 'object' ? (
                          <pre className="whitespace-pre-wrap">{JSON.stringify(value, null, 2)}</pre>
                        ) : (
                          <p>{String(value)}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        );
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading strategic framework analysis...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <AlertTriangle className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Analysis</h3>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  // Show message when no research is selected
  if (!selectedResearch) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <div className="text-center">
          <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Research Selected</h3>
          <p className="text-gray-600">Please select a research project to view the strategic framework analysis.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-sm p-8 border border-gray-100">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Strategic Framework</h1>
        <p className="text-gray-600">
          Comprehensive strategic analysis for {selectedResearch.title} including business strategy, market positioning,
          competitive analysis, and strategic frameworks to guide decision-making.
        </p>
      </div>

      {/* Dynamic Sections */}
      {sections.map((section) => {
        const Icon = section.icon;
        return (
          <div key={section.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full flex items-center justify-between p-6 text-left hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                <Icon className="w-5 h-5 text-blue-600" />
                <h2 className="text-xl font-semibold text-gray-900">{section.name}</h2>
              </div>
              {expandedSection === section.id ? <ChevronDown className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
            </button>

            {expandedSection === section.id && renderSectionContent(section.id)}
          </div>
        );
      })}
    </div>
  );
}