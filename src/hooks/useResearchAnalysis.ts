import { useState, useEffect } from 'react';
import { DatabaseService, ResearchAnalysis } from '../lib/supabase';

interface UseResearchAnalysisResult {
  data: Record<string, any>;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseResearchAnalysisOptions {
  researchId?: string;
  sectionPrefix?: string;
  sections?: string[];
}

/**
 * Custom hook to load research analysis data from Supabase
 * @param options Configuration options
 * @returns Analysis data, loading state, error state, and refetch function
 */
export function useResearchAnalysis(options: UseResearchAnalysisOptions): UseResearchAnalysisResult {
  const { researchId, sectionPrefix, sections } = options;
  const [data, setData] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    if (!researchId) {
      setData({});
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Get all analysis data for the research
      const analyses = await DatabaseService.getResearchAnalysis(researchId);
      
      // Filter analyses based on criteria
      let filteredAnalyses = analyses;
      
      if (sectionPrefix) {
        filteredAnalyses = analyses.filter(analysis => 
          analysis.section.startsWith(sectionPrefix)
        );
      }
      
      if (sections && sections.length > 0) {
        filteredAnalyses = analyses.filter(analysis => 
          sections.includes(analysis.section)
        );
      }

      // Transform analyses into a more usable format
      const analysisData: Record<string, any> = {};
      
      filteredAnalyses.forEach(analysis => {
        analysisData[analysis.section] = analysis.analysis_data;
      });

      setData(analysisData);
    } catch (err) {
      console.error('Error fetching research analysis:', err);
      setError(err instanceof Error ? err.message : 'Failed to load analysis data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [researchId, sectionPrefix, sections?.join(',')]);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
}

/**
 * Hook specifically for dashboard data
 */
export function useDashboardAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sections: ['dashboard_synthesis']
  });
}

/**
 * Hook specifically for strategic framework data
 */
export function useStrategicFrameworkAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sectionPrefix: 'strategic_'
  });
}

/**
 * Hook specifically for MVP data
 */
export function useMVPAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sectionPrefix: 'mvp_'
  });
}

/**
 * Hook specifically for USP data
 */
export function useUSPAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sectionPrefix: 'usp_'
  });
}

/**
 * Hook specifically for customer personas data
 */
export function useCustomerPersonasAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sections: ['customer_personas']
  });
}

/**
 * Hook specifically for finances data
 */
export function useFinancesAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sectionPrefix: 'finances_'
  });
}

/**
 * Hook specifically for go-to-market data
 */
export function useGoToMarketAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sectionPrefix: 'gtm_'
  });
}

/**
 * Hook specifically for competitive analysis data
 */
export function useCompetitiveAnalysis(researchId?: string) {
  return useResearchAnalysis({
    researchId,
    sectionPrefix: 'competitive_'
  });
}
